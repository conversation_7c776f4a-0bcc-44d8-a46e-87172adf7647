//@version=5
indicator("Volume Tape Histogram", "Tape", true, max_bars_back=0)

// Colors for the histogram
upColor = color.blue     // Blue for upticks
dnColor = color.red      // Red for downticks

// Simple approach - use current bar data
var float prevClose = na
var float upVolume = 0.0
var float dnVolume = 0.0

// Reset volumes at start of new bar
if barstate.isnew
    upVolume := 0
    dnVolume := 0

// Check if we have previous close to compare
if not na(prevClose)
    // Determine direction based on close vs previous close
    priceChange = close - prevClose

    if priceChange > 0
        // Price went up - this is buying volume
        upVolume := volume
        dnVolume := 0
    else if priceChange < 0
        // Price went down - this is selling volume
        upVolume := 0
        dnVolume := volume
    else
        // No price change
        upVolume := 0
        dnVolume := 0
else
    // First bar, no comparison possible
    upVolume := 0
    dnVolume := 0

// Update previous close for next comparison
prevClose := close

// Plot the histogram bars
plot(upVolume, color=upColor, style=plot.style_histogram, linewidth=2, title="Up Volume")
plot(-dnVolume, color=dnColor, style=plot.style_histogram, linewidth=2, title="Down Volume")

// Add a zero line for reference
hline(0, "Zero Line", color=color.gray, linestyle=hline.style_dashed)
