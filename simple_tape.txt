//@version=5
indicator("Volume Tape Histogram", "Tape", true, max_bars_back=0)

// Colors for the histogram
upColor = color.blue     // Blue for upticks
dnColor = color.red      // Red for downticks

// Initialize core variables
varip vdiff = array.new_float(2, 0), varip vsize = 0.0, varip newVolume = false
varip upVolume = 0.0, varip dnVolume = 0.0

// Get price and volume from 1 minute chart
[ltf_p, ltf_v] = request.security_lower_tf(syminfo.tickerid, "1", [close, volume])

// Core logic
if barstate.isnew
    vdiff.fill(0)

// Calculate new volume size
barVol = ltf_v.sum()
if barVol != vdiff.last()
    vdiff.push(barVol), vdiff.shift()
    vsize := vdiff.range()
    newVolume := true

// Update volume when there is new volume
if barstate.isrealtime and newVolume and vsize > 0

    // Determine direction
    currentPrice = ltf_p.last()
    prevPrice = ltf_p.size() > 1 ? ltf_p.get(ltf_p.size() - 2) : currentPrice

    isUp = currentPrice > prevPrice
    isDn = currentPrice < prevPrice

    // Set volume based on direction
    if isUp
        upVolume := vsize
        dnVolume := 0
    else if isDn
        upVolume := 0
        dnVolume := vsize
    else
        upVolume := 0
        dnVolume := 0
else
    upVolume := 0
    dnVolume := 0

// Plot the histogram bars
plot(upVolume, color=upColor, style=plot.style_histogram, linewidth=3, title="Up Volume")
plot(-dnVolume, color=dnColor, style=plot.style_histogram, linewidth=3, title="Down Volume")

// Add a zero line for reference
hline(0, "Zero Line", color=color.gray, linestyle=hline.style_dashed)
