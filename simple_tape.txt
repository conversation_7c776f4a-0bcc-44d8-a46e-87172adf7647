//@version=5
indicator("Volume Tape Histogram", "Tape", true, max_bars_back=500)

// Settings
lookback = input.int(14, "Lookback Period", minval=1, maxval=100, tooltip="Number of bars to look back for smoothing")
smoothing = input.int(3, "Smoothing Length", minval=1, maxval=20, tooltip="Length for smoothing the volume bars")

// Colors for the histogram
upColor = color.blue     // Blue for upticks
dnColor = color.red      // Red for downticks

// Simple approach - use current bar data
var float prevClose = na
var float upVolume = 0.0
var float dnVolume = 0.0

// Calculate price change over lookback period
priceChange = ta.change(close, lookback)
volumeToUse = volume

// Determine direction based on price change over lookback period
if priceChange > 0
    // Price trend is up over lookback period
    upVolume := volumeToUse
    dnVolume := 0
else if priceChange < 0
    // Price trend is down over lookback period
    upVolume := 0
    dnVolume := volumeToUse
else
    // No significant price change
    upVolume := 0
    dnVolume := 0

// Apply smoothing to the volume data
smoothedUpVolume = ta.sma(upVolume, smoothing)
smoothedDnVolume = ta.sma(dnVolume, smoothing)

// Plot the smoothed histogram bars
plot(smoothedUpVolume, color=upColor, style=plot.style_histogram, linewidth=2, title="Up Volume")
plot(-smoothedDnVolume, color=dnColor, style=plot.style_histogram, linewidth=2, title="Down Volume")

// Add a zero line for reference
hline(0, "Zero Line", color=color.gray, linestyle=hline.style_dashed)
