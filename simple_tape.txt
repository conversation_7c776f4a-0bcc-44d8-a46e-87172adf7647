//@version=5
indicator("Volume Tape Histogram", "Tape", true, max_bars_back=500)

// Colors for the histogram
upColor = color.blue     // Blue for upticks
dnColor = color.red      // Red for downticks

// Create arrays for tracking volume and direction
varip upVol = array.new_float()
varip dnVol = array.new_float()
varip barIndex = array.new_int()

// Initialize core variables
varip vdiff = array.new_float(2, 0), varip vsize = 0.0, varip newVolume = false
varip currentBarIndex = 0

// Get price and volume from 1 minute chart
[ltf_p, ltf_v] = request.security_lower_tf(syminfo.tickerid, "1", [close, volume])

// Core logic loop
for Fewer_Performance_Penalties_Over_Time = 0 to 0

    // Reset on each new bar
    if barstate.isnew
        vdiff.fill(0)
        currentBarIndex += 1

    // Calculate new volume size
    barVol = ltf_v.sum()
    if barVol != vdiff.last()
        vdiff.push(barVol), vdiff.shift()
        vsize := vdiff.range()
        newVolume := true

    // Update arrays when there is new volume
    if barstate.isrealtime and newVolume and vsize > 0

        // Determine direction
        currentPrice = ltf_p.last()
        prevPrice = ltf_p.size() > 1 ? ltf_p.get(ltf_p.size() - 2) : currentPrice

        isUp = currentPrice > prevPrice
        isDn = currentPrice < prevPrice

        // Add volume to appropriate array
        if isUp
            upVol.unshift(vsize)
            dnVol.unshift(0)
        else if isDn
            upVol.unshift(0)
            dnVol.unshift(vsize)
        else
            upVol.unshift(0)
            dnVol.unshift(0)

        barIndex.unshift(currentBarIndex)

        // Keep only last 100 entries
        if upVol.size() > 100
            upVol.pop()
            dnVol.pop()
            barIndex.pop()

    newVolume := false

// Plot the histogram bars
if upVol.size() > 0
    for i = 0 to math.min(upVol.size() - 1, 99)
        upVolume = upVol.get(i)
        dnVolume = dnVol.get(i)
        barIdx = barIndex.get(i)

        // Plot up volume as positive bars
        if upVolume > 0
            plot(upVolume, color=upColor, style=plot.style_histogram, linewidth=2, offset=-i, title="Up Volume")

        // Plot down volume as negative bars
        if dnVolume > 0
            plot(-dnVolume, color=dnColor, style=plot.style_histogram, linewidth=2, offset=-i, title="Down Volume")
